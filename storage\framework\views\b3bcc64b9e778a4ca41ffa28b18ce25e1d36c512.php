<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Quotation - <?php echo e($quotation->quotation_number); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
        }
        .quotation-title {
            font-size: 15px;
            font-weight: bold;
        }
        .quotation-number {
            font-size: 16px;
            color: #666;
        }
        .details-section {
            margin: 20px 0;
        }
        .details-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .details-row {
            display: table-row;
        }
        .details-cell {
            display: table-cell;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .label {
            font-weight: bold;
            width: 30%;
        }
        .value {
            width: 70%;
        }
        .pricing-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .pricing-table th,
        .pricing-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .pricing-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .pricing-table .text-right {
            text-align: right;
        }
        .total-section {
            margin-top: 20px;
            text-align: right;
        }
        .total-amount {
            font-size: 18px;
            font-weight: bold;
            color: #2563eb;
        }
        .notes-section {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #2563eb;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <img style="width: 150px; height: 100px;" src="data:image/png;base64,<?php echo e(base64_encode(file_get_contents(public_path('/uploads/companyprofile/artsy.png')))); ?>" alt="logo">
        <div class="quotation-title">QUOTATION</div>
    </div>

    <div class="details-section">
        <h3>Quotation Details</h3>
        <div class="details-grid">
            <div class="details-row">
                <div class="details-cell label">Client Name:</div>
                <div class="details-cell value"><?php echo e($quotation->lead->client_name); ?></div>
            </div>
            <div class="details-row">
                <div class="details-cell label">County:</div>
                <div class="details-cell value"><?php echo e($quotation->county->name ?? 'N/A'); ?></div>
            </div>
            <div class="details-row">
                <div class="details-cell label">Date:</div>
                <div class="details-cell value"><?php echo e($quotation->created_at->format('F d, Y')); ?></div>
            </div>
            <div class="details-row">
                <div class="details-cell label">Quotation No:</div>
                <div class="details-cell value"><?php echo e($quotation->quotation_number); ?></div>
            </div>
        </div>
    </div>

    <div class="details-section">
        <h3>Product Specifications</h3>
        <div class="details-grid">
            <div class="details-row">
                <div class="details-cell label">Dimensions:</div>
                <div class="details-cell value"><?php echo e($quotation->lead->dimensions); ?></div>
            </div>
            <div class="details-row">
                <div class="details-cell label">Open Size:</div>
                <div class="details-cell value"><?php echo e($quotation->lead->open_size); ?></div>
            </div>
            <div class="details-row">
                <div class="details-cell label">Box Style:</div>
                <div class="details-cell value"><?php echo e($quotation->lead->box_style); ?></div>
            </div>
            <div class="details-row">
                <div class="details-cell label">Stock:</div>
                <div class="details-cell value"><?php echo e($quotation->lead->stock); ?></div>
            </div>
            <div class="details-row">
                <div class="details-cell label">Lamination:</div>
                <div class="details-cell value"><?php echo e($quotation->lead->lamination); ?></div>
            </div>
            <div class="details-row">
                <div class="details-cell label">Printing:</div>
                <div class="details-cell value"><?php echo e($quotation->lead->printing); ?></div>
            </div>
            <?php if($quotation->lead->add_ons): ?>
            <div class="details-row">
                <div class="details-cell label">Add-ons:</div>
                <div class="details-cell value"><?php echo e($quotation->lead->add_ons); ?></div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="details-section">
        <h3>Pricing Details</h3>
        <table class="pricing-table">
            <thead>
                <tr>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th class="text-right">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php if($quotation->lead->qty_1 && $quotation->price_qty_1): ?>
                <tr>
                    <td><?php echo e(number_format($quotation->lead->qty_1)); ?> pcs</td>
                    <td><?php echo e($currencySymbol); ?><?php echo e(number_format($quotation->price_qty_1, 2)); ?></td>
                    <td class="text-right"><?php echo e($currencySymbol); ?><?php echo e(number_format($quotation->lead->qty_1 * $quotation->price_qty_1, 2)); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($quotation->lead->qty_2 && $quotation->price_qty_2): ?>
                <tr>
                    <td><?php echo e(number_format($quotation->lead->qty_2)); ?> pcs</td>
                    <td><?php echo e($currencySymbol); ?><?php echo e(number_format($quotation->price_qty_2, 2)); ?></td>
                    <td class="text-right"><?php echo e($currencySymbol); ?><?php echo e(number_format($quotation->lead->qty_2 * $quotation->price_qty_2, 2)); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($quotation->lead->qty_3 && $quotation->price_qty_3): ?>
                <tr>
                    <td><?php echo e(number_format($quotation->lead->qty_3)); ?> pcs</td>
                    <td><?php echo e($currencySymbol); ?><?php echo e(number_format($quotation->price_qty_3, 2)); ?></td>
                    <td class="text-right"><?php echo e($currencySymbol); ?><?php echo e(number_format($quotation->lead->qty_3 * $quotation->price_qty_3, 2)); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($quotation->lead->qty_4 && $quotation->price_qty_4): ?>
                <tr>
                    <td><?php echo e(number_format($quotation->lead->qty_4)); ?> pcs</td>
                    <td><?php echo e($currencySymbol); ?><?php echo e(number_format($quotation->price_qty_4, 2)); ?></td>
                    <td class="text-right"><?php echo e($currencySymbol); ?><?php echo e(number_format($quotation->lead->qty_4 * $quotation->price_qty_4, 2)); ?></td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <?php if($quotation->notes): ?>
    <div class="notes-section">
        <h4>Notes:</h4>
        <p><?php echo e($quotation->notes); ?></p>
    </div>
    <?php endif; ?>

    <div class="footer">
        <p>Generated by <?php echo e($quotation->creator->first_name ?? 'System'); ?> <?php echo e($quotation->creator->last_name ?? ''); ?> on <?php echo e(now()->format('F d, Y')); ?></p>
        <p>This quotation is valid until <?php echo e($quotation->valid_until ? $quotation->valid_until->format('F d, Y') : 'further notice'); ?></p>
    </div>
</body>
</html>
<?php /**PATH C:\xampp8\htdocs\artsy\resources\views/quotations/pdf.blade.php ENDPATH**/ ?>